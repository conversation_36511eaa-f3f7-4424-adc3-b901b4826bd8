import { scale, verticalScale } from 'react-native-size-matters';

// Shared card dimensions to ensure consistency across components
export const CARD_DIMENSIONS = {
  // Standard card width used in AcademyProfile for Coach and Course cards
  STANDARD_CARD_WIDTH: scale(250),

  // Standard card heights for different card types
  COACH_CARD_IMAGE_HEIGHT: verticalScale(150),
  COURSE_CARD_IMAGE_HEIGHT: verticalScale(180),

  // Standard border radius
  CARD_BORDER_RADIUS: scale(8),

  // Standard border properties
  CARD_BORDER_WIDTH: scale(1),
  CARD_BORDER_COLOR: '#E0E0E0',
};
