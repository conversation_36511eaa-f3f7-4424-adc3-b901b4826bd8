import React, { useEffect, useState } from 'react';
import { Text, View, ScrollView, Image, TouchableOpacity, ActivityIndicator, } from 'react-native';
import { ScaledSheet } from 'react-native-size-matters';
import Footer from '../components/footer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { useRoute } from '@react-navigation/native'
import Search from '../components/Search';
import { NEXT_PUBLIC_BASE_URL, RAZORPAY_KEY } from '@env';
import axios from 'axios';
import { useAuth } from '../Context/AuthContext';

import LinearGradient from 'react-native-linear-gradient';
const confirmationIcon = require('../../src/assets/Confirmation.png');

const ThankYou = () => {
  const route = useRoute();
  const [isLoading, setIsLoading] = useState(true);
  const { login, setUser, isLoggedIn, userToken } = useAuth();
  const getBookingId = route.params.BookingId;
  const [thankyouDetails, setThankyouDetails] = useState();
  const navigation = useNavigation();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const requestOptions = {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${userToken}`,
          },
        };
        const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/booking/${getBookingId}`, requestOptions);
        setThankyouDetails(response.data);
      } catch (error) {
        console.error(error);
      }
      finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [getBookingId]);
  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const options = { day: "numeric", month: "long", year: "numeric" };
    return date.toLocaleDateString("en-US", options);
  };
  const formatTime = (timeString) => {
    if (!timeString) {
      return "";
    }
    const [hours, minutes] = timeString.split(":");
    const time = new Date(1970, 0, 1, hours, minutes);
    return time.toLocaleTimeString("en-US", {
      hour: "numeric",
      minute: "2-digit",
    });
  };

  let subtotal = thankyouDetails?.classes?.reduce(
    (accu, x) => accu + x.fees,
    0
  );
  subtotal = thankyouDetails?.coachId?.hasGst ? subtotal / 1.18 : subtotal;
  const coachGst =
    thankyouDetails?.coach_id?.affiliationType !== "academy"
      ? thankyouDetails?.coachId?.hasGst
        ? subtotal * 0.18
        : 0
      : thankyouDetails?.academy_id?.gstNumber
        ? subtotal * 0.18
        : 0;
  const platformFee = thankyouDetails?.academyId?.platformFee
    ? thankyouDetails.academyId.platformFee / 100
    : 0.12;

  const platformTax = subtotal * platformFee;

  // const platformTax = subtotal * 0.12;
  const taxGST = platformTax * 0.18;
  const total = Math.ceil(subtotal + platformTax + taxGST + coachGst);
  const bookingDate = new Date(thankyouDetails?.createdAt.split("T")[0]).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
  return (
    <ScrollView>
      <View style={styles.mainContainer}>
        {/* <Search /> */}
        <LinearGradient
          colors={['rgba(227, 31, 38, 0.1)', 'rgba(0, 190, 439, 0.5)']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 0 }}
          style={styles.linearGradient}
        >
          <View style={styles.section}>
            <View style={styles.columns}>
              <View style={[styles.confirmationSection, styles.confirmationPadding]}>
                <Text style={styles.thankYouText}>A GREAT BIG THANK YOU!</Text>

                <View style={styles.paymentConfirmation}>
                  <Image source={confirmationIcon} style={styles.confirmationIcon} />
                  <View>
                    <Text style={styles.confirmationText}>Payment Confirmed!</Text>
                    <Text style={styles.confirmationSubtext}>Ordered on {bookingDate}</Text>
                    <Text style={styles.confirmationSubtext}>Booking id: {thankyouDetails?.bookingId}</Text>
                  </View>
                </View>

                <View style={styles.invoiceLinksContainer}>
                  {[{
                  label: "Platform Invoice",
                  screen: "Invoice",
                  },
                    {
                    label: "Coach Invoice",
                    screen: "CoachInvoice",
                  },
                    {
                    label: "Order Summary",
                    screen: "OrderSummary",
                  },
                  ].map((item, index) => (
                      <TouchableOpacity key={index} onPress={() => navigation.navigate(item.screen, { BookingId: getBookingId })}>
                        <Text style={styles.invoiceLinkText}>{item.label}</Text>
                      </TouchableOpacity>
                    ))}
                </View>
              </View>
              <View style={styles.subSection}>
                <Text style={styles.detailsLabel}>Details</Text>
                <View style={styles.row}>
                  <Text style={styles.label}>Course Name:</Text>
                  <Text style={styles.value}>{thankyouDetails?.courseId?.courseName}</Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Coach Name:</Text>
                  <Text style={styles.value}>{thankyouDetails?.coachId?.firstName} {thankyouDetails?.coachId?.lastName}</Text>
                </View>
                <View style={styles.row}>
                  <Text style={styles.label}>Venue:</Text>
                  <Text style={styles.value}>{thankyouDetails?.courseId?.facility?.name || thankyouDetails?.courseId?.facility?.addressLine1}
                    {thankyouDetails?.courseId?.facility?.addressLine1 && thankyouDetails?.courseId?.facility?.name ? `, ${thankyouDetails?.courseId?.facility?.addressLine1}` : ""}
                    {thankyouDetails?.courseId?.facility?.addressLine2 ? `, ${thankyouDetails?.courseId?.facility?.addressLine2}` : ""}
                    {thankyouDetails?.courseId?.facility?.city ? `, ${thankyouDetails?.courseId?.facility?.city}` : ""}
                    {thankyouDetails?.courseId?.facility?.country ? `, ${thankyouDetails?.courseId?.facility?.country}` : ""}</Text>
                </View>
              </View>
              <View style={styles.subSection}>
                <Text style={styles.detailsLabel}>Payment Details</Text>
                <View style={styles.row}>
                  <Text style={styles.label}>Payment Method:</Text>
                  <Text style={styles.value}>
                    {thankyouDetails?.paymentMode && thankyouDetails?.wallet
                      ? `${thankyouDetails?.paymentMode} & Wallet`
                      : thankyouDetails?.paymentMode
                        ? thankyouDetails?.paymentMode.toUpperCase()
                        : "Wallet"}
                  </Text>
                </View>
                {thankyouDetails?.paymentId && (
                  <View style={styles.row}>
                    <Text style={styles.label}>Payment id:</Text>
                    <Text style={styles.value}>{thankyouDetails?.paymentId}</Text>
                  </View>


                )}
                <View style={styles.row}>
                  <Text style={styles.label}>Transaction Id:</Text>
                  <Text style={styles.value}>{thankyouDetails?.razorPayPaymentId ? thankyouDetails?.razorPayPaymentId?.toUpperCase() : thankyouDetails?.bookingId}</Text>
                </View>
                {thankyouDetails?.wallet && (
                  <View style={styles.row}>
                    <Text style={styles.label}>Wallet Amount:</Text>
                    <Text style={styles.value}>{thankyouDetails?.walletAmount.toFixed(2)}</Text>
                  </View>


                )}
              </View>
              <View style={styles.subSection}>
                <Text style={styles.detailsLabel}>Booking Details</Text>
                <ScrollView contentContainerStyle={styles.scrollViewContent}>
                  {thankyouDetails?.classes?.map((x, index) => (
                    <View style={[styles.bookingDateRow, styles.bookingItemContainer]} key={index}>
                      <View style={styles.row}>
                        <Text style={styles.label}>Dates :</Text>
                        <Text style={styles.value}>
                          {`${new Date(x.date).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' })} (${x?.days})`}
                        </Text>
                      </View>
                      <View style={styles.row}>
                        <Text style={styles.label}>Time :</Text>
                        <Text style={styles.value}>
                          {`${formatTime(x.startTime)} - ${formatTime(x.endTime)}`}
                        </Text>
                      </View>
                    </View>
                  ))}
                </ScrollView>

              </View>
            </View>

            <View style={styles.paymentRow}>
              <View style={styles.amounRow}>
                <Text style={styles.totalAmount}>Subtotal ({thankyouDetails?.courseType})</Text>
                <Text style={styles.totalAmount}>₹ {subtotal?.toFixed(2)}</Text>
              </View>
              <View style={styles.amounRow}>
                <Text style={styles.totalAmount}>Platform Fee (12%)</Text>
                <Text style={styles.totalAmount}>₹ {platformTax?.toFixed(2)}</Text>
              </View>
              <View style={styles.amounRow}>
                <Text style={styles.totalAmount}>GST (18%)</Text>
                <Text style={styles.totalAmount}>₹ {taxGST?.toFixed(2)}</Text>
              </View>
              {/* <View style={styles.amounRow}>
                <Text style={styles.totalAmount}>CoachGST (18%)</Text>
                <Text style={styles.totalAmount}>₹ {coachGst?.toFixed(2)}</Text>
              </View> */}
              <View style={styles.amounRow}>
                <Text style={styles.totalLabel}>Total</Text>
                <Text style={styles.totalLabel}>₹ {total.toFixed(2)}</Text>
              </View>
            </View>
            <View style={styles.horizontalLine} />
            {/* {renderSocialIcons()} */}
          </View>


        </LinearGradient>
        <Footer />
      </View>
    </ScrollView>
  );
};

export default ThankYou;

const styles = ScaledSheet.create({
  linearGradient: {
    flex: 1,
  },
  container: {
    paddingHorizontal: '10@s'
  },
  section: {
    width: "90%",
    marginVertical: '20@vs',
    marginHorizontal: '20@s',
    backgroundColor: "#F9FAFB",
    borderRadius: '10@s',
  },
  columns: {
    justifyContent: "center",
    alignItems: "center",
  },
  subSection: {
    width: "90%",
    marginVertical: '15@vs',
    borderRadius: '5@s',
    backgroundColor: '#fff',
    borderWidth: '0.8@s',
    borderColor: '#ccc',
    paddingHorizontal: '8@s',
    paddingVertical: '12@vs'
  },
  thankYouText: {
    fontSize: '20@ms',
    fontWeight: '600',
    textAlign: 'center',
    color: '#000',
    marginVertical: '12@vs',
    letterSpacing: '1@s',
  },
  confirmationSection: {
    justifyContent: "center",
    alignItems: "center",
    marginTop: '12@vs',
  },
  paymentConfirmation: {
    flexDirection: 'row',
    alignItems: 'center',
    width: "90%",
    gap: '10@s',
    alignSelf: 'flex-start',
  },
  confirmationIcon: {
    width: '35@s',
    height: '35@s',
    resizeMode: 
  },
  confirmationText: {
    fontSize: '15@ms',
    color: '#0EA5E9',
  },
  confirmationSubtext: {
    fontSize: '14@ms',
    color: '#000',
    lineHeight: '16@vs'
  },
  row: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginVertical: '3@vs',
    paddingHorizontal: '2@s',
    gap: '8@s',
  },
  detailsLabel: {
    fontSize: '16@ms',
    fontWeight: '500',
    marginBottom: '6@vs',
    textAlign: 'left',
    color: "black"
  },
  label: {
    fontSize: '13@ms',
    fontWeight: '500',
    color: 'black',
    minWidth: '100@s',
    maxWidth: '110@s',
  },
  value: {
    flex: 1,
    fontSize: '13@ms',
    color: 'black',
    textAlign: 'left',
  },
  paymentRow: {
    paddingHorizontal: '12@s',
  },
  amounRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: '12@vs',
  },
  logo: {
  },
  paymentText: {
    fontSize: '15@ms',
    color: "grey",
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: '5@vs',
    color: "grey"
  },
  totalLabel: {
    fontSize: '16@ms',
    color: "#000",
    fontWeight: "500"
  },
  totalAmount: {
    fontSize: '16@ms',
    color: "grey",
  },
  horizontalLine: {
    backgroundColor: '#BCBEC0',
    height: '1@s',
    width: '90%',
    alignSelf: 'center',
    marginVertical: '15@vs'
  },
  shareSection: {
    width: '100%',
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: '20@vs',
  },
  shareHeading: {
    fontSize: '16@ms',
    marginBottom: '10@vs',
    color: "#000",
  },
  socialIconsContainer: {
    alignItems: 'center',
    margin: '10@s',
  },
  socialIcon: {
    width: '20@s',
    height: '20@s',
  },
  scrollView: {
    flexDirection: 'column',
  },
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  mainContainer: {
    flex: 1,
  },
  bookingDateRow: {
    marginVertical: '5@vs',
  },
  confirmationPadding: {
    paddingHorizontal: '10@s',
  },
  invoiceLinksContainer: {
    flexDirection: 'row',
    justifyContent: "flex-start",
    alignItems: "center",
    flexWrap: 'wrap',
    gap: '10@s',
    marginTop: '15@vs',
  },
  invoiceLinkText: {
    color: "#0EA5E9",
    fontSize: '14@ms',
  },
  scrollViewContent: {
    flexGrow: 1
  },
  bookingItemContainer: {
    alignItems: "flex-start",
    width: "95%",
    marginVertical: '5@vs',
    paddingHorizontal: '5@s',
    paddingVertical: '3@vs',
  },
});
