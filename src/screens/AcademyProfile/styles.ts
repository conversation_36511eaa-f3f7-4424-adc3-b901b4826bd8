import { ScaledSheet, vs } from 'react-native-size-matters';
import { TextStyle } from 'react-native';

export const styles = ScaledSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
  },
  contentContainer: {
    paddingHorizontal: '10@s',
    paddingTop: '10@vs',
    paddingBottom: '5@vs',
    gap: "15@vs"
  },
  // Loading styles
  loaderContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: '20@vs',
  },
  loadingText: {
    marginTop: '10@vs',
    fontSize: '16@ms',
    color: '#666',
    fontFamily: 'Lato-Regular',
  },
  // Error styles
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: '20@s',
    backgroundColor: 'white',
  },
  errorTitle: {
    fontSize: '24@ms',
    fontWeight: 'bold',
    color: '#333',
    marginBottom: '10@vs',
    fontFamily: 'Lato-Bold',
  },
  errorMessage: {
    fontSize: '16@ms',
    color: '#666',
    textAlign: 'center',
    marginBottom: '30@vs',
    lineHeight: '24@ms',
    fontFamily: 'Lato-Regular',
  },
  buttonContainer: {
    flexDirection: 'row',
    gap: '15@s',
  },
  retryButton: {
    backgroundColor: '#E31F26',
    paddingHorizontal: '25@s',
    paddingVertical: '12@vs',
    borderRadius: 6,
  },
  retryButtonText: {
    color: 'white',
    fontSize: '16@ms',
    fontWeight: '600',
    fontFamily: 'Lato-Bold',
  },
  goBackButton: {
    backgroundColor: 'transparent',
    paddingHorizontal: '25@s',
    paddingVertical: '12@vs',
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#E31F26',
  },
  goBackButtonText: {
    color: '#E31F26',
    fontSize: '16@ms',
    fontWeight: '600',
    fontFamily: 'Lato-Bold',
  },
  logo: {
    height: '67@vs',
    width: '168@s',
  },
  title: {
    fontSize: '28@ms',
    fontFamily: 'Lato-SemiBold',
    color: '#000',
    lineHeight: '52.27@ms',
    letterSpacing: '0.84@ms',
  },
  description: {
    fontSize: '18@ms',
    lineHeight: '30.68@ms',
    color: '#2B2B2A',
    fontFamily: 'Lato-Regular',
    marginBottom: '10@vs',
  },
  descriptionLi: {
    margin: 0,
    paddingHorizontal: '2@s',
    color: '#2B2B2A',
    alignItems: 'center',
    fontFamily: 'Lato-Regular',
    fontSize: '18@ms',
    lineHeight: '30.68@ms',
    marginBottom: '6@vs',
  },
  descriptionOl: {
    marginHorizontal: '5@s',
    alignItems: 'center',
    padding: 0,
    fontFamily: 'Lato-Regular',
    fontSize: '18@ms',
    lineHeight: '30.68@ms',
    marginBottom: '6@vs',
  },
  descriptionUl: {
    marginHorizontal: '5@s',
    alignItems: 'center',
    padding: 0,
    fontFamily: 'Lato-Regular',
    fontSize: '18@ms',
    lineHeight: '30.68@ms',
    marginBottom: '6@vs',
  },
  descriptionH2: {
    margin: 0,
    padding: 0,
    color: '#2B2B2A',
    fontSize: '20@ms',
    fontFamily: 'Lato-Bold',
    marginVertical: '8@vs',
  },
  heading: {
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: vs(16),
    color: '#2B2B2A',
    fontFamily: 'Lato-Bold',
  },
  profileImage: {
    width: "100%",
    height: '200@vs',
    borderRadius: '10@s',
    alignSelf: 'center',
    marginBottom: '16@vs'
  },
  testimonialContainer: {
    marginHorizontal: '-20@s'
  },
});

const justify: TextStyle['textAlign'] = 'justify';

// Base styles for all HTML tags
const baseHtmlStyles = {
  color: '#2B2B2A',
  fontSize: 18,
  lineHeight: 30.68,
  fontFamily: 'Lato-Regular'
};

export const htmlTagStyles = {
  p: { 
    ...baseHtmlStyles,
    textAlign: justify, 
    marginBottom: vs(4), 
    marginTop: 0,
  },
  li: { 
    ...baseHtmlStyles,
    textAlign: justify, 
    marginBottom: vs(2), 
    marginTop: 0,
  },
  ol: { 
    ...baseHtmlStyles,
    textAlign: justify, 
    marginBottom: vs(2), 
    marginTop: 0,
  },
  ul: { 
    ...baseHtmlStyles,
    textAlign: justify, 
    marginBottom: vs(2), 
    marginTop: 0,
  },
  h1: { 
    ...baseHtmlStyles,
    marginBottom: vs(4), 
    marginTop: 0,
  },
  h2: { 
    ...baseHtmlStyles,
    textAlign: justify, 
    marginBottom: vs(6), 
    marginTop: 0,
  },
  h3: { ...baseHtmlStyles },
  h4: { ...baseHtmlStyles },
  h5: { ...baseHtmlStyles },
  h6: { ...baseHtmlStyles },
  span: { ...baseHtmlStyles },
  strong: { ...baseHtmlStyles },
  em: { ...baseHtmlStyles },
  b: { ...baseHtmlStyles },
  i: { ...baseHtmlStyles },
  a: { 
    ...baseHtmlStyles,
    textDecorationLine: 'none' as const,
  },
  div: { ...baseHtmlStyles },
};
