import React, { useEffect, useState } from 'react';
import { StyleSheet, Text, View, ScrollView, Image, TouchableOpacity, ActivityIndicator, PermissionsAndroid, Alert, Linking, Platform } from 'react-native';
import { check, PERMISSIONS, RESULTS } from 'react-native-permissions';
import Footer from '../components/footer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { useNavigation } from '@react-navigation/native';
import { useRoute } from '@react-navigation/native'
import Search from '../components/Search';
import { NEXT_PUBLIC_BASE_URL, RAZORPAY_KEY } from '@env';
import axios from 'axios';
import { useAuth } from '../Context/AuthContext';
import LinearGradient from 'react-native-linear-gradient';
const confirmationIcon = require('../../src/assets/Confirmation.png');
import RNFetchBlob from 'rn-fetch-blob'
import RNFS from 'react-native-fs';
import RNHTMLtoPDF from 'react-native-html-to-pdf';
import * as ScopedStorage from "react-native-scoped-storage"
const khelo = require('../assets/Khelo.png')
import { State } from "country-state-city";



const CoachInvoice = () => {
    const route = useRoute();
    const [isLoading, setIsLoading] = useState(true);
    const { login, setUser, isLoggedIn, userToken } = useAuth();
    const getBookingId = route.params.BookingId;
    const [thankyouDetails, setThankyouDetails] = useState();

    const [playerHomeState, setPlayerHomeState] = useState("");
    const [registeredState, setRegisteredState] = useState("Delhi");

    const [gstStateName, setGstStateName] = useState("");
    const [facilityState, setFacilityState] = useState("");

    const countryCode = 'IN';

    useEffect(() => {
        const fetchData = async () => {
            try {
                const requestOptions = {
                    headers: {
                        "Content-Type": "application/json",
                        Authorization: `Bearer ${userToken}`,
                    },
                };
                const response = await axios.get(`${NEXT_PUBLIC_BASE_URL}/api/booking/${getBookingId}`, requestOptions);
                setThankyouDetails(response);
                const gstState = State.getStateByCodeAndCountry(
                    response?.data?.coachId?.gstState,
                    "IN"
                )?.name || "";

                const facilityState = response?.data?.courseId?.facility?.state;

                setGstStateName(gstState);
                setFacilityState(facilityState);
             
            } catch (error) {
                console.error(error);
            }
            finally {
                setIsLoading(false);
            }
        };

        fetchData();
        // requestStoragePermission();
    }, [getBookingId, userToken]);



    if (isLoading) {
        return (
            <View style={styles.loaderContainer}>
                <ActivityIndicator size="large" color="#0000ff" />
            </View>
        );
    }
    const formatDate = (dateString) => {
        const date = new Date(dateString);
        const options = { day: "numeric", month: "long", year: "numeric" };
        return date.toLocaleDateString("en-US", options);
    };
    const formatTime = (timeString) => {
        if (!timeString) {
            return "";
        }
        const [hours, minutes] = timeString.split(":");
        const time = new Date(1970, 0, 1, hours, minutes);
        return time.toLocaleTimeString("en-US", {
            hour: "numeric",
            minute: "2-digit",
        });
    };

    let subtotal = thankyouDetails?.data?.classes.reduce((accu, x) => accu + x.fees, 0)
    subtotal = thankyouDetails?.data.coachId?.hasGst ? subtotal / 1.18 : subtotal;

    const platformTax = subtotal * 0.12
    const taxGST = thankyouDetails?.data.coachId?.hasGst ? subtotal * 0.18 : 0;

    const total = Math.ceil(subtotal + platformTax + taxGST)
  const isSameState = gstStateName === facilityState;
    const cgst = isSameState ? taxGST / 2 : 0;
    const sgst = isSameState ? taxGST / 2 : 0;
    const igst = taxGST;

    let from = `Umn Khel Shiksha Private Limited on, behalf of: ${thankyouDetails?.data?.coachId?.firstName} ${thankyouDetails?.data?.coachId?.lastName}`;
    let billto = "Dummy title,Dummy address ,Dummy address,Dummy-XXXXX";
    let invoiceno = thankyouDetails?.data?.bookingId;
    let date = new Date(thankyouDetails?.data?.bookingDate).toLocaleDateString('en-IN', { day: 'numeric', month: 'long', year: 'numeric' });
    let due_date = "Apr 7, 2023";
    let total_amount = thankyouDetails?.data?.pricePaid;
    let total_due = "25.00";
    let gstid = "07AADCU2822L1Z8";
    let servicefee_box = "25.00";
    let totalamount_box = thankyouDetails?.data?.pricePaid;


    const createPDF = async () => {
        // const hasPermission = requestStoragePermission();
        // if (!hasPermission) return;
        const base64Image = 'data:image/png;base64,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'
        const html = `
        <!DOCTYPE html>
        <html>
        <head>
            <style>
            body {
                font-family: 'Lato', sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #fff;
                color: #000;
            }
            .diagonalMain{
                position:relative
            }
            .diagonalMain img {
                width: 100%;
                height: 50%;
            }
            .textContainer {
                position: absolute;
                top: 70%;
                right: 32px;
                color: white;
                font-size: 20px;
                font-weight: bold;
            }
            .inv-pg-banner img{
                width:100%;
                height: auto;
                max-height: 200px;
                object-fit: cover;
                object-position: center;
            }
            
            #invoice_Details p{
                margin: 0;
                font-style: normal;
                line-height: normal;
            }
            .cs-flex{
                display: flex;
              align-items: center;
              
            }
            
            .align-center{
                align-items: center;
            }
            
            .inv-info{
                display: grid;
                gap: 1rem;
                margin-block: 1rem 2rem;
            }
            
            .inv-info .cs-flex{
                gap:2%;
            }
            
            .color-red{
                color: red;
            }
            .color-lightgrey{
                color: grey;
            }
            .text-bold{
                font-weight:bold;
            }
            
            .inv-payment-details{
                border: 2px solid #23c1ed;
                .cs-flex span{
                    flex: 1 1 0;
                    justify-content: space-between;
                    gap:5px;
                }
            }
            
            .inv-payment-details > div:not(:last-child){
                border-bottom: 2px solid #23c1ed;
            }
            .inv-payment-details > div{
                padding-inline: 0.7em;
                padding-block:0.5rem;
            }
            .detailRow, .infoColumnKey, .tableRow {
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            .detailColumn {
                display: flex;
                flex-direction: column;
                margin-left: 5px;
            }
            .table {
                width: 100%;
                border-collapse: collapse;
            }
            .table, .table th, .table td {
                border: 2px solid rgb(4, 196, 225);
                padding: 8px;
                text-align: left;
            }
            .table th {
                background-color: rgb(240, 246, 247);
                color: red;
                font-weight: bold; 
            }
            .totalAmount {
                color: red;
                font-weight: bold;
            }
            .gstText {
                margin-top: 10px;
                font-size: 15px;
            }
            .dwnld-invoice{
                background: unset;
                border: unset;
                offset: unset;
                color: #000;
                margin-bottom: 8px;
                font-weight: 500;
                cursor: pointer;
            }
            .dwnld-invoice:hover{
                offset: unset;
                outline: unset;
            }
            </style>
        </head>
        <body>
        <div class="diagonalMain">
        <img src="${base64Image}"alt="Khelo Logo">
        <div class="textContainer">COACH INVOICE</div>
    </div>
    <div class="inv-info">
    <div class="addressfrom cs-flex">
        <span class="text-bold">From:</span>
        <div class="detailColumn">
          ${from.split(",").map(item => `<span>${item}</span>`).join("")}
        </div>

    </div>
  
      <div class="invAmout cs-flex">
        <span class="text-bold">GST ID:</span>
        <span class="detailColumn">${gstid}</span>
    </div>
    <div class="row">
       <span class="text-bold"><strong>Invoice No:</strong></span>
      <span >${invoiceno}</span></div>
    <div class="invdate cs-flex">
    <strong>Date:</strong><span>${date}</span>
        
    </div>
    <div class="invBilling cs-flex">
        <span class="text-bold">Bill To:</span>
        <div class="billDetails">
        <p>${thankyouDetails?.data?.playerName}</p>
        <p>${thankyouDetails?.data?.player?.mobile}</p>
        <p>${thankyouDetails?.data?.playerEmail}</p>
        </div>
    </div>

</div>
<div class="inv-payment-details">
    <div class="Description_title cs-flex">
        <span class="color-red text-bold">Description/memo</span>
        <span class="color-red text-bold">Total Amount</span>
    </div>
    <div class="payment-breaks cs-flex">
    <span class="color-lightgrey">Service Fee</span>
    <span class="color-lightgrey">${subtotal.toFixed(2)}</span>  
</div>

${isSameState  && `
    <div class="payment-breaks cs-flex">
        <span class="color-lightgrey">CGST(9%)</span>
        <span class="color-lightgrey">₹${cgst?.toFixed(2)}</span>
    </div>
    <div class="payment-breaks cs-flex">
        <span class="color-lightgrey">SGST(9%)</span>
        <span class="color-lightgrey">₹${sgst?.toFixed(2)}</span>
    </div>
` }
${(!isSameState && thankyouDetails?.coachId?.hasGst) && `
    <div class="payment-breaks cs-flex">
        <span class="color-lightgrey">IGST(18%)</span>
        <span class="color-lightgrey">₹${igst?.toFixed(2)}</span>
    </div>
`}

<div class="Description_title cs-flex">
    <span class="color-red text-bold">Total Amount</span>
    <span class="color-red text-bold">₹${(subtotal + taxGST).toFixed(2)}</span>
</div>
</div>
</div>
        </body>
        </html>
        `;
        if (Platform.OS === 'android') {
            try {
                const options = {
                    html: html,
                    fileName: 'invoice',
                    base64: true,
                };

                const file = await RNHTMLtoPDF.convert(options);
                let dir = await ScopedStorage.openDocumentTree(true);
                const timestamp = new Date().getTime();
                const fileName = `invoice_${timestamp}.pdf`;
                const mimeType = "application/pdf";
                const encoding = "base64";
                await ScopedStorage.writeFile(dir.uri, file.base64, fileName, mimeType, encoding);
                await AsyncStorage.setItem('userMediaDirectory', JSON.stringify(dir));
                Alert.alert('PDF Created', `PDF file has been created and saved in the selected directory.`);
            } catch (error) {
                console.error(error);
                Alert.alert('Error', 'An error occurred while creating the PDF.');
            }
        } else {
            console.log("----IOS DEVICE")
        }

    };
    return (
        <ScrollView style={styles.container}>
            <View style={styles.diagonalMain}>
                <View><Image source={khelo} style={{ width: 380, height: 150 }} /></View>
                <View style={styles.textContainer}><Text style={styles.invoiceText}>COACH INVOICE</Text></View>
            </View>
            <View style={styles.informationSection}>
                <View>
                    <View style={styles.detailRow}>
                        <Text style={{ color: "#000", fontFamily: "Lato-Bold", fontSize: 16 }}>From :</Text>
                        <View style={styles.detailColumn}>
                            {from.split(",").map((item, index) => (
                                <Text style={{ color: "#000", fontFamily: "Lato-Regular", fontSize: 14 }} key={index}>{item}</Text>
                            ))}
                        </View>
                    </View>
                    <Text style={styles.gstText}>GST ID : {gstid}</Text>
                </View>


                <View style={styles.invoiceInfo}>
                    <View style={styles.infoColumnKey}>
                        <View style={styles.row}>
                            <Text style={styles.textCon}>Invoice No:</Text>
                            <Text style={styles.valueWith}>{invoiceno}</Text>
                        </View>
                        <View style={styles.row}>
                            <Text style={styles.textCon}>Date:</Text>
                            <Text style={styles.valueWith}>{date}</Text>
                        </View>
                    </View>
                </View>
                {/* </View> */}
                <View style={styles.billToContainer}>
                    <View style={styles.detailRow}>
                        <Text style={{ color: "#000", fontFamily: "Lato-Bold", fontSize: 16 }}>Bill To:</Text>
                        <View style={styles.detailColumn}>
                            <Text style={{ color: "#000", fontFamily: "Lato-Regular", fontSize: 16 }}>{thankyouDetails?.data?.playerName}</Text>
                            <Text style={{ color: "#000", fontFamily: "Lato-Regular", fontSize: 16 }}>{thankyouDetails?.data?.playerEmail}</Text>
                            <Text style={{ color: "#000", fontFamily: "Lato-Regular", fontSize: 16 }}>{thankyouDetails?.data?.player?.mobile}</Text>
                        </View>
                    </View>
                </View>
                <View style={styles.table}>
                    <View style={styles.tableRow}>
                        <Text style={styles.tableHeader}>DESCRIPTION/MEMO</Text>
                        <Text style={styles.tableHeader}>TOTAL AMOUNT</Text>
                    </View>
                    <View style={styles.tableRow}>
                        <Text style={styles.tableCell}>Base Price</Text>
                        <Text style={styles.tableCell}>{subtotal.toFixed(2)}</Text>
                    </View>
                    {isSameState && (
                        <>
                            <View style={styles.tableRow}>
                                <Text style={styles.tableCell}>CGST(9%)</Text>
                                <Text style={styles.tableCell}>₹{cgst?.toFixed(2)}</Text>
                            </View>
                            <View style={styles.tableRow}>
                                <Text style={styles.tableCell}>SGST(9%)</Text>
                                <Text style={styles.tableCell}>₹{sgst?.toFixed(2)}</Text>
                            </View>

                        </>
                    )}
                    {(!isSameState && thankyouDetails?.coachId?.hasGst) && (
                        <>
                            <View style={styles.tableRow}>
                                <Text style={styles.tableCell}>IGST(18%)</Text>
                                <Text style={styles.tableCell}>₹{igst?.toFixed(2)}</Text>
                            </View>
                        </>
                    )}
                    <View style={styles.tableRow}>
                        <Text style={[styles.tableCell, styles.totalAmount]}>TOTAL AMOUNT</Text>
                        <Text style={[styles.tableCell, styles.totalAmount]}>₹{(subtotal + taxGST).toFixed(2)}</Text>
                    </View>

                </View>
            </View>
            <TouchableOpacity onPress={createPDF} style={{marginBottom:"15%"}}>
                <View
                    style={{
                        width: "100%",
                        height: 35,
                        borderRadius: 3,
                        backgroundColor: "#0EA5E9",
                        display: "flex",
                        justifyContent: "center",
                        alignItems: "center",
                        marginVertical: "2%"
                    }}
                >
                    <Text style={{ fontSize: 14, color: "#fff" }}>
                        Download Coach Invoice
                    </Text>
                </View>
            </TouchableOpacity>
        </ScrollView>
    );
};

export default CoachInvoice;

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: '#fff',
        padding: "5%",
    },
    diagonalMain: {
        position: 'relative',
        overflow: 'hidden',
    },
    informationSection: {

    },
    textContainer: {
        position: 'absolute',
        top: '70%',
        right: 32,
        zIndex: 2,
        // transform: [{ translateY: -50 }],
    },
    invoiceText: {
        fontSize: 20,
        color: 'white',
        fontFamily: "Lato-Bold"
    },
    detailRow: {
        flexDirection: 'row',
        gap: 4,
        marginTop: "2%"
    },
    detailColumn: {
        flexDirection: 'column',
        textAlign: 'left',
        marginLeft: "2%"
    },
    gstText: {
        marginTop: "4%",
        fontSize: 16,
        fontFamily: "Lato-Regular",
        color: "#000"
    },
    invoiceInfo: {
        flexDirection: 'row',
        gap: 16,
        marginTop: "2%"
    },
    infoColumn: {
        flexDirection: 'column',
        textAlign: 'left',
        width: "70%"
    },
    infoColumnKey: {
        flexDirection: 'column',
        textAlign: 'left',
        // padding: "3%",
        width: "100%"
    },
    infoLabel: {
        fontSize: 16,
        fontFamily: "Lato-Bold",
        color: "#000"
    },
    infoValue: {
        fontSize: 16,
        fontFamily: "Lato-Regular",
        color: "#000"
    },
    billToContainer: {
        flexDirection: 'row',
        justifyContent: 'space-between',

        marginBottom: 40,
    },
    table: {
        borderWidth: 2,
        borderColor: 'rgb(4, 196, 225)',
        backgroundColor: 'rgb(240, 246, 247)',
        width: '100%',
    },
    tableRow: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 8,
        borderBottomWidth: 2,
        borderBottomColor: 'rgb(4, 196, 225)',
    },
    tableHeader: {
        fontWeight: 'bold',
        color: 'red',
        width: '50%',
    },
    tableCell: {
        width: '50%',
        
    },
    totalAmount: {
        color: 'red',
        fontWeight: 'bold',
    },
    row: { flexDirection: 'row', alignItems: 'center', marginBottom: 8, color: "#000" },
    textCon: { width: 100, color: "#000", fontFamily: 'Lato-Bold', fontSize: 16, width: "30%" },
    valueWith: { color: "#000", width: "70%", fontFamily: 'Lato-Regular', fontSize: 15 }
});

