import { Text, View, TouchableOpacity } from 'react-native';
import { ScaledSheet } from 'react-native-size-matters';
import Image from '@d11/react-native-fast-image';
import React from 'react';
import { useNavigation } from '@react-navigation/native';


const redirect = require('../../assets/redirect.png');

const TopAcademyCard = ({ image, title, academyId, academyImages }) => {
  const navigation = useNavigation();
  const handleNavigation = ()=>{
    navigation.navigate("AcademyProfile", {id: academyId, academyImages, title, profileImg: image});
  }
  
  const renderImageContent = () => {
    if (image && image.uri) {
      return <Image source={image} style={styles.image} resizeMode="stretch" />;
    } else {
      return (
        <View style={styles.placeholderContainer}>
          <Text style={styles.placeholderText}>No Image</Text>
        </View>
      );
    }
  };

  return (
    <TouchableOpacity style={styles.card} onPress={handleNavigation}>
      <View style={styles.imageContainer}>
        {renderImageContent()}
      </View>
      <View style={styles.body}>
        <Text style={styles.title} numberOfLines={1}>{title}</Text>
        <View style={styles.learnMore} >
          <Text style={styles.learnMoreText}>View Academy</Text>
          <Image source={redirect} style={{ width: 15, height: 15, marginLeft: 6 }} />
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = ScaledSheet.create({
  card: {
    width: '250@s',
    height: '300@vs',
    borderRadius: '8@s',
    backgroundColor: '#fff',
    overflow: 'hidden',
    borderWidth: '1@s',
    borderColor: '#E0E0E0',
  },
  imageContainer: {
    height: '225@vs', // 75% of 300
    width: '100%',
    borderTopLeftRadius: '8@s',
    borderTopRightRadius: '8@s',
  },
  image: {
    width: '100%',
    height: '100%',
    borderTopLeftRadius: '8@s',
    borderTopRightRadius: '8@s',
    backgroundColor: "#d3d3d3",
    resizeMode: 'stretch',
  },
  placeholderContainer: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
    borderTopLeftRadius: '8@s',
    borderTopRightRadius: '8@s',
  },
  placeholderText: {
    fontSize: '16@ms',
    color: '#999',
    fontFamily: 'Lato-Regular',
  },
  body: {
    height: '75@vs', // 25% of 300
    padding: '12@s',
    justifyContent: 'space-between',
  },
  title: {
    fontSize: '16@ms',
    color: 'black',
    fontFamily: 'Lato-Bold',
  },
  button: {
    backgroundColor: '#1e90ff',
    borderRadius: '8@s',
    paddingVertical: '8@vs',
    alignItems: 'center',
    marginTop: 'auto',
  },
  learnMore: {
    flexDirection: 'row',
    alignItems: 'center',
    borderRadius: '4@s',
    marginTop: 'auto',
  },
  learnMoreText: {
    fontWeight: 'bold',
    color: '#000',
    paddingBottom: '2@vs',
    borderBottomWidth: '1@s',
    borderBottomColor: '#000',
  },
});

export default TopAcademyCard;