import { ScaledSheet } from 'react-native-size-matters';

export default ScaledSheet.create({
  card: {
    width: '250@s',
    borderWidth: '1@s',
    borderColor: '#E0E0E0',
    borderRadius: '8@s',
    backgroundColor: '#FFF',
    overflow: 'hidden',
  },
  image: {
    width: '100%',
    height: '150@vs',
    borderTopLeftRadius: '8@s',
    borderTopRightRadius: '8@s',
    objectFit: 'fill',
  },
  cardContent: {
    padding: '8@s',
    justifyContent: 'space-evenly',
    height: '150@vs',
  },
  coachName: {
    fontSize: '16@ms',
    color: 'black',
    fontFamily: 'Lato-Bold',
  },
  category: {
    fontSize: '14@ms',
    color: 'black',
    fontFamily: 'Lato-Regular',
  },
  description: {
    fontSize: '14@ms',
    color: 'grey',
    marginTop: '4@vs',
    fontFamily: 'Lato-Regular',
  },
  learnMore: {
    borderRadius: '4@s',
    display: 'flex',
    flexDirection: 'row',
    alignItems: 'center',
  },
  learnMoreText: {
    fontWeight: 'bold',
    color: '#000',
    paddingBottom: '2@vs',
    borderBottomWidth: '1@s',
    borderBottomColor: '#000',
  },
});